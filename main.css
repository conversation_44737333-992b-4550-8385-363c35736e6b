/* ===== SHARED STYLES FOR ALL PAGES ===== */

/* ===== CSS VARIABLES ===== */
:root {
    --black-pearl: #062034;
    --loblolly: #c5d1d5;
    --nevada: #64767e;
    --granny-smith: #7b949a;
    --river-bed: #495d62;
    --river-bed-alt: #44535c;
    --white: #ffffff;
    --button-color: #7b949a;
    --hover-button-color: #062034;
}

/* ===== GLOBAL STYLES ===== */
body {
    font-family: 'Open Sans', sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--white);
    color: var(--black-pearl);
    line-height: 1.8;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

/* ===== HEADER STYLES ===== */
.header {
    background-color: var(--white);
    padding: 15px 35px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1000;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
}

.logo-container img {
    max-width: 110px;
    margin-right: 15px;
}

.logo-container h1 {
    font-size: 30px;
    color: var(--black-pearl);
    margin: 0;
    font-weight: 700;
    line-height: 1.2;
}

.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-menu a {
    color: var(--black-pearl);
    text-decoration: none;
    font-weight: 700;
    font-size: 16px;
    transition: color 0.3s ease, font-size 0.3s ease;
}

.nav-menu a:hover {
    color: var(--button-color);
    font-size: 18px;
}

.nav-menu a.active {
    color: var(--button-color);
}

.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
    padding: 10px 0;
    border-radius: 5px;
    z-index: 1000;
}

.dropdown-content a {
    display: block;
    padding: 10px 20px;
    color: var(--black-pearl);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.dropdown-content a:hover {
    background-color: var(--loblolly);
    color: var(--white);
}

.dropdown:hover .dropdown-content {
    display: block;
}

/* ===== MOBILE MENU ===== */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 20px;
    height: 2px;
    background-color: var(--black-pearl);
    margin: 2px 0;
    transition: 0.3s;
    border-radius: 1px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-4px, 4px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-4px, -4px);
}

/* ===== SUPPLEMENTEN PAGE SPECIFIC STYLES ===== */

.content-container {
    display: flex;
    gap: 20px;
    padding: 20px;
    align-items: stretch;
}

.filter-menu {
    flex: 0 0 300px;
    background-color: var(--loblolly);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.filter-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.filter-section h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--black-pearl);
    border-bottom: 1px solid var(--nevada);
    padding-bottom: 5px;
}

.categories ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.categories ul li {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.categories ul li input[type="checkbox"] {
    margin-right: 10px;
    accent-color: var(--button-color);
    transform: scale(1.2);
}

.categories ul li span {
    font-size: 14px;
    color: var(--nevada);
}

.price-filter {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.price-range {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.price-range input[type="number"] {
    width: 60px;
    padding: 5px;
    border: 1px solid var(--nevada);
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
}

.price-range span {
    font-size: 18px;
    font-weight: 700;
    color: var(--black-pearl);
}

.rating-filter {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.rating-filter label {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-filter input[type="radio"] {
    accent-color: var(--button-color);
    transform: scale(1.2);
}

.rating-filter .stars {
    font-size: 14px;
    color: var(--granny-smith);
}

.product-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 0;
    background-color: var(--white);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.product-table th,
.product-table td {
    padding: 15px;
    text-align: center;
    border: 1px solid var(--nevada);
}

.product-table th {
    background-color: var(--loblolly);
    font-size: 16px;
    font-weight: 700;
    color: var(--black-pearl);
}

.product-table td {
    font-size: 14px;
    color: var(--black-pearl);
}

.product-table img {
    width: 100%;
    height: 100%;
    max-width: 260px;
    max-height: 260px;
    object-fit: cover;
    border-radius: 5px;
}

.product-table .button {
    padding: 10px 15px;
    background: var(--button-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
    white-space: nowrap;
}

.product-table .button:hover {
    background: var(--hover-button-color);
}

/* ===== PRICE SLIDER STYLING ===== */
.price-boxes {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.price-box input[type="number"] {
    width: 50px;
    border: none;
    outline: none;
    text-align: right;
    font-size: 1em;
    font-weight: bold;
    background: #fff;
    padding: 0;
}

/* Pijltjes zichtbaar maken voor Chrome/Safari */
.price-box input[type="number"]::-webkit-inner-spin-button,
.price-box input[type="number"]::-webkit-outer-spin-button {
    opacity: 1;
    margin: 0;
    cursor: pointer;
}

/* Hidden range inputs for price slider functionality */
#min-price-range,
#max-price-range {
    display: none;
}

/* Euroteken verwijderd (titel toont nu "Prijs (€)") */
#min-price-box span.currency,
#max-price-box span.currency {
    display: none;
}

#min-price-box,
#max-price-box {
    width: 60px;
    height: 60px;
    border: 1px solid #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    font-weight: bold;
}

/* ===== SLIDER CONTAINER EN TRACK ===== */
.price-slider-container {
    position: relative;
    height: 30px;
}

.price-slider-track {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: #ccc;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 1;
}

.slider-handle {
    position: absolute;
    top: 50%;
    width: 20px;
    height: 20px;
    background: #333;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 100;
    user-select: none;
    touch-action: none;
}

.reset-smooth {
    background-color: #ffdddd;
    transition: background-color 0.5s ease;
}

/* ===== RESULTATEN EN SORTERING ===== */
.results-sorting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.results-info {
    font-size: 1em;
    font-weight: bold;
}

.sort-menu {
    display: flex;
    align-items: center;
}

/* ===== STERREN VOOR BEOORDELING ===== */
.star {
    font-size: 16px;
    vertical-align: middle;
    margin: 0 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.filled-star {
    color: #F05D23;
    text-shadow: 0 1px 2px rgba(240, 93, 35, 0.3);
}

.empty-star {
    color: #999;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* ===== FILTER HEADER ===== */
.category-header {
    margin-bottom: 15px;
    border-bottom: 2px solid var(--loblolly);
    padding-bottom: 8px;
}

/* ===== ACTIVE FILTERS SECTION ===== */
.active-filters-section {
    margin-bottom: 20px;
    display: none; /* Hidden by default */
}

.active-filters-list {
    margin-bottom: 15px;
}

.active-filter-item {
    display: inline-block;
    background: var(--granny-smith);
    color: var(--white);
    padding: 6px 12px;
    margin: 4px 8px 4px 0;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.reset-filters-btn {
    background: var(--nevada);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    width: 100%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.reset-filters-btn:hover {
    background: var(--black-pearl);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

/* Hide all toggle buttons completely */
.top-toggle,
.sidebar-toggle,
.filter-toggle {
    display: none !important;
}

/* Mobile filter overlay */
.filter-menu.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw !important;
    height: 100vh !important;
    background: var(--white);
    z-index: 9999;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.filter-menu.mobile-overlay .filter-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 80px; /* Space for fixed button */
}

.mobile-filter-apply {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: var(--loblolly);
    padding: 12px 20px;
    margin: 0;
    z-index: 10002;
}

.mobile-filter-apply button {
    width: 100%;
    padding: 12px;
    font-size: 14px;
    font-weight: bold;
    background: var(--granny-smith);
    color: var(--white);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-filter-apply button:hover {
    background: var(--hover-button-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Content container aanpassingen */
.content-container {
    transition: all 0.3s ease;
    display: flex;
    gap: 20px;
}

.content-container.filters-hidden {
    display: block;
}

.content-container.filters-hidden .product-table-container {
    width: 100%;
    margin: 0;
}

/* ===== RESPONSIVE DESIGN - MOBILE FRIENDLY ===== */

/* Tablet and smaller screens */
@media (max-width: 1024px) {
    .header {
        padding: 10px 20px;
    }

    .logo-container img {
        max-width: 80px;
    }

    .nav-menu {
        gap: 15px;
    }

    .content-container {
        gap: 15px;
        padding: 0 15px;
    }

    .filter-menu {
        min-width: 200px;
    }
}

/* Mobile screens */
@media (max-width: 768px) {
    /* Header responsive - more compact */
    .header {
        padding: 10px 15px;
        text-align: center;
        position: relative;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .logo-container {
        margin-bottom: 0;
        margin-left: 50px; /* Space for hamburger menu */
    }

    .logo-container img {
        max-width: 50px;
        margin-right: 8px;
    }

    .logo-container h1 {
        font-size: 1.2em;
        margin: 0;
    }

    /* Mobile navigation */
    .nav-menu {
        position: absolute;
        top: 100%;
        left: 15px;
        right: 15px;
        background: var(--white);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        flex-direction: column;
        padding: 10px;
        border-radius: 8px;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        max-width: 250px;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-menu a {
        padding: 8px 12px;
        margin: 3px 0;
        border-radius: 5px;
        background: var(--loblolly);
        color: var(--black-pearl);
        text-decoration: none;
        text-align: center;
        font-size: 14px;
    }

    .nav-menu a:hover,
    .nav-menu a.active {
        background: var(--granny-smith);
        color: var(--white);
    }

    /* Content layout mobile - horizontal with compact filters */
    .content-container {
        flex-direction: row;
        gap: 5px;
        padding: 8px;
        align-items: flex-start; /* Align to top */
    }

    /* Filter menu mobile - initially hidden, clean overlay when shown */
    .filter-menu {
        display: none !important; /* Initially completely hidden on mobile */
    }

    /* Mobile filter button above table */
    .mobile-filter-button-container {
        margin-bottom: 10px;
        width: 100%;
        display: none; /* Hidden by default, shown via JavaScript */
    }

    .mobile-filter-button {
        width: 100%;
        background: var(--granny-smith);
        color: var(--white);
        border: none;
        padding: 12px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s ease;
    }

    .mobile-filter-button:hover {
        background: #2d7a2d;
    }

    /* When filters are shown on mobile, use overlay */
    .filter-menu.mobile-overlay {
        display: flex !important;
        background: var(--loblolly);
        border: none;
        border-radius: 0;
        padding: 50px 8px 8px 8px; /* Extra top padding for close button */
    }

    /* Mobile filter close button (kruisje) */
    .mobile-filter-close {
        position: absolute;
        top: 15px;
        right: 15px;
        background: var(--granny-smith);
        color: var(--white);
        border: none;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }

    .mobile-filter-close:hover {
        background: #2d7a2d;
    }

    /* Clean mobile filter styling - extra compact with desktop-like background */
    .filter-menu.mobile-overlay .filter-section {
        margin: 8px 20px;
        background: var(--loblolly);
        border-radius: 0;
        padding: 8px;
        border: none;
    }

    .filter-menu.mobile-overlay .filter-section h3 {
        font-size: 16px;
        margin-bottom: 8px;
        color: var(--black-pearl);
        font-weight: bold;
    }

    /* Remove white background from price section specifically */
    .filter-menu.mobile-overlay .filter-section.price {
        background: transparent !important;
        padding: 0 !important;
        margin: 8px 20px;
        border: none !important;
    }

    .filter-menu.mobile-overlay .filter-section.price h3 {
        background: transparent !important;
        padding: 0 !important;
        margin-bottom: 8px;
        font-size: 16px;
        color: var(--black-pearl);
        font-weight: bold;
    }

    .filter-menu.mobile-overlay .filter-section ul li {
        margin-bottom: 4px;
        font-size: 14px;
    }

    .filter-menu.mobile-overlay .filter-section label {
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }

    .filter-menu.mobile-overlay .filter-section label:hover {
        background-color: var(--loblolly);
    }

    /* Mobile overlay active filters styling */
    .filter-menu.mobile-overlay .active-filters-section {
        margin-bottom: 20px;
        background: transparent;
        padding: 0;
    }

    .filter-menu.mobile-overlay .active-filters-list {
        margin-bottom: 15px;
    }

    .filter-menu.mobile-overlay .active-filter-item {
        display: inline-block;
        background: var(--granny-smith);
        color: var(--white);
        padding: 8px 12px;
        margin: 4px 8px 4px 0;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .filter-menu.mobile-overlay .reset-filters-btn {
        background: var(--nevada);
        color: var(--white);
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.2s ease;
        width: 100%;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .filter-menu.mobile-overlay .reset-filters-btn:hover {
        background: var(--black-pearl);
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    }

    .filter-menu.mobile-overlay .category-header {
        display: none; /* Hide the entire category header on mobile */
    }

    .filter-menu.mobile-overlay .category-header h3 {
        margin: 0;
        font-size: 11px;
        color: var(--black-pearl);
    }

    /* Mobile overlay price styling - same as desktop */
    .filter-menu.mobile-overlay .price-boxes {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        background: transparent !important;
        padding: 0 !important;
    }

    .filter-menu.mobile-overlay .price-box {
        flex: 1;
        padding: 0 !important;
        border: 1px solid #ccc;
        border-radius: 0;
        background: #fff !important;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 50px;
    }

    .filter-menu.mobile-overlay .price-box input[type="number"] {
        width: 50px;
        border: none;
        outline: none;
        text-align: right;
        font-size: 1em;
        font-weight: bold;
        background: #fff;
        padding: 0;
    }

    /* Pijltjes zichtbaar maken voor Chrome/Safari op mobile */
    .filter-menu.mobile-overlay .price-box input[type="number"]::-webkit-inner-spin-button,
    .filter-menu.mobile-overlay .price-box input[type="number"]::-webkit-outer-spin-button {
        opacity: 1;
        margin: 0;
        cursor: pointer;
    }

    .filter-menu.mobile-overlay .price-slider-container {
        height: 40px;
        margin: 15px 0;
        background: var(--loblolly);
        border-radius: 0;
        padding: 8px;
        border: none;
        position: relative;
    }

    .filter-menu.mobile-overlay .price-slider-track {
        height: 4px;
        background: #ccc;
        border-radius: 0;
        border: none;
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        pointer-events: none;
        z-index: 1;
    }

    .filter-menu.mobile-overlay .slider-handle {
        width: 20px;
        height: 20px;
        background: #333;
        border: none;
        box-shadow: none;
        border-radius: 50%;
        cursor: pointer;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 100;
        user-select: none;
        touch-action: none;
    }

    .product-table-container {
        flex: 1;
        overflow: auto;
        min-width: 0;
        width: 100%;
        padding: 0; /* No padding when filters are hidden */
    }

    /* Table mobile - fully responsive and larger */
    .product-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin: 0;
        padding: 0;
        position: relative;
    }

    .product-table {
        width: 100%;
        min-width: 500px; /* Ensure minimum width for proper layout */
        font-size: 13px;
    }

    .product-table th,
    .product-table td {
        padding: 8px 6px;
        font-size: 13px;
        white-space: nowrap;
    }

    /* Hide less important columns on mobile but keep action column visible */
    .product-table th:nth-child(5), /* Details */
    .product-table td:nth-child(5) {
        display: none;
    }

    /* Ensure action column is always visible and prominent */
    .product-table th:nth-child(6) { /* Actie header */
        position: sticky;
        right: 0;
        background: var(--loblolly); /* Same as other headers */
        color: var(--black-pearl); /* Same as other headers */
        box-shadow: -2px 0 4px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .product-table td:nth-child(6) { /* Actie cells */
        position: sticky;
        right: 0;
        background: var(--white);
        box-shadow: -2px 0 4px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .product-table img {
        max-width: 100px; /* Larger and more prominent */
        max-height: 100px;
        border-radius: 6px; /* Modern rounded corners */
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Subtle shadow for prominence */
    }

    /* Optimized column widths for mobile - flexible height */
    .product-table {
        height: 100%; /* Fill container height */
        width: 100%;
    }

    .product-table th:nth-child(1), /* Product */
    .product-table td:nth-child(1) {
        width: 150px; /* Even larger for more prominent images */
    }

    .product-table th:nth-child(2), /* Naam */
    .product-table td:nth-child(2) {
        min-width: 100px; /* Smaller for product names */
    }

    .product-table th:nth-child(3), /* Prijs */
    .product-table td:nth-child(3) {
        width: 70px;
    }

    .product-table th:nth-child(4), /* Beoordeling */
    .product-table td:nth-child(4) {
        width: 90px;
    }

    .product-table th:nth-child(6), /* Actie */
    .product-table td:nth-child(6) {
        width: 90px;
    }

    /* Results sorting mobile - much smaller and left aligned */
    .results-sorting {
        flex-direction: row;
        gap: 10px;
        align-items: center;
        justify-content: flex-start; /* Left align */
        margin-bottom: 8px;
        padding: 3px 0;
    }

    .results-info {
        display: none; /* Hide results count on mobile */
    }

    .sort-menu {
        font-size: 10px; /* Smaller text */
        flex-shrink: 0;
    }

    .sort-menu span {
        display: none; /* Hide "Sortering:" text on mobile */
    }

    .sort-menu select {
        padding: 3px 5px;
        font-size: 10px; /* Smaller select */
        border-radius: 3px;
        border: 1px solid var(--nevada);
        background: var(--white);
        min-width: 120px; /* Smaller width */
        height: 24px; /* Smaller height */
    }

    /* Button mobile - more prominent */
    .button {
        padding: 8px 12px;
        font-size: 12px;
        background: var(--granny-smith);
        color: var(--white);
        border: none;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        min-width: 70px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .button:hover {
        background: var(--hover-button-color);
        transform: translateY(-1px);
        box-shadow: 0 3px 6px rgba(0,0,0,0.15);
    }

    /* Stars mobile - cleaner */
    .star {
        font-size: 14px;
        margin: 0;
    }
}

/* Small mobile screens */
@media (max-width: 480px) {
    .header {
        padding: 8px 10px;
    }

    .logo-container img {
        max-width: 40px;
        margin-right: 6px;
    }

    .logo-container h1 {
        font-size: 1.1em;
    }

    .content-container {
        padding: 10px;
    }

    /* Price boxes smaller */
    #min-price-box,
    #max-price-box {
        width: 60px;
        height: 60px;
    }

    .price-box input[type="number"] {
        width: 50px;
    }

    /* Table even more compact */
    .product-table th,
    .product-table td {
        padding: 6px 4px;
        font-size: 12px;
    }

    .product-table img {
        max-width: 70px; /* Much larger for better visibility */
        max-height: 70px;
        border-radius: 4px; /* Rounded corners */
        box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* Subtle shadow */
    }
}

/* ===== INDEX PAGE SPECIFIC STYLING ===== */

/* Override content-container for index page - vertical layout */
body:not(.supplementen-page) .content-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    padding: 0; /* Remove padding to allow full width */
}

/* Main content wrapper for index page - gradient background */
body:not(.supplementen-page) .main-content-wrapper {
    background: linear-gradient(to right, var(--black-pearl), var(--river-bed-alt));
    padding: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    width: 100vw; /* Full viewport width */
    margin-left: calc(-50vw + 50%); /* Center and extend to full width */
}

body:not(.supplementen-page) .main-content {
    padding: 40px;
    max-width: 900px;
}

body:not(.supplementen-page) .main-content h1 {
    color: var(--white);
    font-size: 36px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
}

body:not(.supplementen-page) .main-content p {
    color: var(--white);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

body:not(.supplementen-page) .see-all-supplements {
    margin-top: 10px;
    padding: 12px 24px;
    background-color: var(--granny-smith);
    color: var(--white);
    font-weight: 700;
    text-decoration: none;
    border-radius: 5px;
    display: inline-block;
    transition: background-color 0.3s ease;
}

body:not(.supplementen-page) .see-all-supplements:hover {
    background-color: var(--black-pearl);
}

/* Categories section - overlapping style */
body:not(.supplementen-page) .categories-section {
    padding: 20px;
    margin-top: -20px; /* Subtle overlap with gradient section */
    background-color: var(--white);
    text-align: center;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

body:not(.supplementen-page) .see-categories {
    font-size: 28px;
    font-weight: 700;
    color: var(--black-pearl);
    margin-bottom: 20px;
}

body:not(.supplementen-page) .categories {
    display: flex;
    justify-content: space-around;
    max-width: 1080px;
    margin: 0 auto;
    gap: 20px;
}

body:not(.supplementen-page) .category-box {
    flex: 1;
    max-width: 240px;
    height: 240px;
    background-color: var(--loblolly);
    border-radius: 10px;
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease;
}

body:not(.supplementen-page) .category-box:hover {
    transform: scale(1.05);
}

body:not(.supplementen-page) .category-box a {
    display: block;
    position: relative;
    text-decoration: none;
}

body:not(.supplementen-page) .category-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

body:not(.supplementen-page) .category-box .see-more {
    position: absolute;
    bottom: 15px;
    right: 15px;
    padding: 8px 16px;
    background-color: var(--granny-smith);
    color: var(--white);
    font-weight: 700;
    text-decoration: none;
    border-radius: 5px;
    z-index: 2;
    transition: background-color 0.3s ease;
}

body:not(.supplementen-page) .category-box .see-more:hover {
    background-color: var(--black-pearl);
}

/* Mobile responsive for index page */
@media screen and (max-width: 1024px) {
    body:not(.supplementen-page) .categories {
        flex-wrap: wrap;
        justify-content: center;
    }

    body:not(.supplementen-page) .category-box {
        flex: 1 1 45%;
        max-width: 45%;
    }
}

@media screen and (max-width: 768px) {
    body:not(.supplementen-page) .main-content-wrapper {
        padding: 20px;
    }

    body:not(.supplementen-page) .main-content {
        padding: 30px;
    }

    body:not(.supplementen-page) .main-content h1 {
        font-size: 28px;
    }

    body:not(.supplementen-page) .main-content p {
        font-size: 14px;
    }

    body:not(.supplementen-page) .categories {
        flex-direction: column;
        gap: 20px;
    }

    body:not(.supplementen-page) .category-box {
        max-width: 100%;
        height: 250px;
    }
}

/* ===== CALORIE CALCULATOR PAGE SPECIFIC STYLES ===== */

/* Calculator page content container override */
body.calculator-page .content-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 20px !important;
    display: block !important;
}

/* SUPER STRONG OVERRIDE - Force horizontal layout at all screen sizes */
.calculator-layout,
body .calculator-layout,
body.calculator-page .calculator-layout {
    display: flex !important;
    flex-direction: row !important;
    gap: 20px !important;
    align-items: flex-start !important;
    flex-wrap: nowrap !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
}

/* FORCE OVERRIDE MEDIA QUERIES */
@media (max-width: 1024px) {
    .calculator-layout,
    body .calculator-layout,
    body.calculator-page .calculator-layout {
        display: flex !important;
        flex-direction: row !important;
        gap: 20px !important;
        align-items: flex-start !important;
        flex-wrap: nowrap !important;
    }
}

@media (max-width: 768px) {
    .calculator-layout,
    body .calculator-layout,
    body.calculator-page .calculator-layout {
        display: flex !important;
        flex-direction: row !important;
        gap: 20px !important;
        align-items: flex-start !important;
        flex-wrap: nowrap !important;
    }
}

.calculator-section,
body .calculator-section,
body.calculator-page .calculator-section {
    display: flex !important;
    gap: 20px !important;
    flex: 1 !important;
    flex-direction: row !important;
}

/* Override the specific media query from supplementen.css */
@media (max-width: 1024px) {
    .calculator-layout,
    body .calculator-layout,
    body.calculator-page .calculator-layout {
        display: flex !important;
        flex-direction: row !important;
        gap: 15px !important;
        align-items: flex-start !important;
        flex-wrap: nowrap !important;
    }

    .calculator-section,
    body .calculator-section,
    body.calculator-page .calculator-section {
        display: flex !important;
        gap: 15px !important;
        flex: 1 !important;
        flex-direction: row !important;
    }

    .calculator-layout .faq-sidebar,
    .calculator-layout .calculator-section,
    .calculator-layout .right-sidebar {
        width: auto !important;
        order: unset !important;
    }
}

@media (max-width: 768px) {
    .calculator-layout,
    body .calculator-layout,
    body.calculator-page .calculator-layout {
        display: flex !important;
        flex-direction: row !important;
        gap: 10px !important;
        align-items: flex-start !important;
        flex-wrap: nowrap !important;
    }

    .calculator-section,
    body .calculator-section,
    body.calculator-page .calculator-section {
        display: flex !important;
        gap: 10px !important;
        flex: 1 !important;
        flex-direction: row !important;
    }

    .faq-sidebar, .test-faq-sidebar {
        width: 180px !important;
        font-size: 0.85em;
    }
}

@media (max-width: 480px) {
    .calculator-layout,
    body .calculator-layout,
    body.calculator-page .calculator-layout {
        display: flex !important;
        flex-direction: row !important;
        gap: 8px !important;
        align-items: flex-start !important;
        flex-wrap: nowrap !important;
    }

    .calculator-section,
    body .calculator-section,
    body.calculator-page .calculator-section {
        display: flex !important;
        gap: 8px !important;
        flex: 1 !important;
        flex-direction: row !important;
    }

    .faq-sidebar, .test-faq-sidebar {
        width: 150px !important;
        font-size: 0.8em;
    }
}

/* Calculator specific sidebar styling to match original compact design */
body.calculator-page .content-container > div:first-child {
    max-width: 1200px;
    margin: 0 auto;
}

/* Ensure calculator sidebars maintain original width */
body.calculator-page .content-container > div:first-child > div:first-child,
body.calculator-page .content-container > div:first-child > div:last-child {
    width: 250px !important;
    flex-shrink: 0 !important;
}

/* Calculator middle section should flex */
body.calculator-page .content-container > div:first-child > div:nth-child(2) {
    flex: 1 !important;
    max-width: none !important;
}

/* Form styling voor cleaner look */
#calorie-form input:focus,
#calorie-form select:focus {
    border-color: var(--granny-smith) !important;
    outline: none;
    box-shadow: 0 0 0 3px rgba(101, 163, 13, 0.1);
}

#calorie-form input:hover,
#calorie-form select:hover {
    border-color: var(--granny-smith);
}

/* Smooth transitions */
#calorie-form input,
#calorie-form select {
    transition: all 0.3s ease;
}

/* Fieldset styling */
#calorie-form fieldset {
    border: 1px solid var(--nevada);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background: rgba(255,255,255,0.3);
}

#calorie-form legend {
    color: var(--black-pearl);
    font-weight: 700;
    font-size: 16px;
    padding: 0 8px;
}

/* Focus states voor accessibility */
#calorie-form input:focus,
#calorie-form select:focus {
    outline: 2px solid var(--granny-smith);
    outline-offset: 2px;
}

/* ===== CALCULATOR LAYOUT STYLES ===== */

/* Main calculator layout */
.calculator-layout {
    display: flex;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    align-items: stretch;
    height: 700px;
}

/* FAQ Sidebar (Links) */
.calculator-faq-sidebar {
    width: 250px;
    background-color: var(--loblolly);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}

.calculator-faq-sidebar h2 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--black-pearl);
}

.faq-item {
    margin-bottom: 20px;
}

.faq-item h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--black-pearl);
    margin-bottom: 8px;
}

.faq-item p {
    font-size: 14px;
    color: var(--nevada);
    line-height: 1.6;
    margin: 0;
}

/* Calculator Main Section */
.calculator-main {
    flex: 1;
    background-color: var(--loblolly);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    flex-direction: column;
}

/* Calculator Header */
.calculator-header {
    text-align: center;
    margin-bottom: 20px;
}

.calculator-header h1 {
    color: var(--black-pearl);
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.calculator-header p {
    color: var(--nevada);
    margin: 5px 0 0 0;
    font-size: 14px;
}

/* Calculator Form Section */
.calculator-form-section {
    background: rgba(255,255,255,0.5);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid var(--nevada);
    margin-bottom: 20px;
}

.form-title {
    color: var(--black-pearl);
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
}

.calculator-form {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.form-fieldset {
    border: 1px solid var(--nevada);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background: rgba(255,255,255,0.3);
}

.form-legend {
    color: var(--black-pearl);
    font-weight: 700;
    font-size: 16px;
    padding: 0 8px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
}

.form-grid-two {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.form-field {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 13px;
    color: var(--black-pearl);
    margin-bottom: 6px;
    font-weight: 600;
}

.form-input {
    padding: 10px;
    border: 2px solid var(--nevada);
    border-radius: 6px;
    font-size: 14px;
    background: var(--white);
    transition: border-color 0.3s;
}

.form-input:focus {
    border-color: var(--granny-smith);
    outline: none;
    box-shadow: 0 0 0 3px rgba(101, 163, 13, 0.1);
}

.form-input:hover {
    border-color: var(--granny-smith);
}

/* Main Result Display */
.main-result {
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 2px solid var(--granny-smith);
    display: none;
}

.result-label {
    font-size: 16px;
    color: var(--nevada);
    margin-bottom: 8px;
    font-weight: 600;
}

.target-calories {
    font-size: 32px;
    font-weight: 700;
    color: var(--black-pearl);
    margin-bottom: 8px;
}

.goal-text {
    font-size: 14px;
    color: var(--nevada);
    font-style: italic;
}

/* BMR/TDEE Results */
.bmr-tdee-results {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
    display: none;
}

.result-box {
    text-align: center;
    padding: 15px;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
    border: 1px solid var(--nevada);
}

.result-title {
    font-size: 12px;
    color: var(--black-pearl);
    font-weight: 600;
    margin-bottom: 4px;
}

.result-subtitle {
    font-size: 10px;
    color: var(--nevada);
    margin-bottom: 6px;
}

.result-value {
    font-size: 16px;
    font-weight: 700;
    color: var(--black-pearl);
}

/* Macros Container */
.macros-container {
    margin-bottom: 20px;
    font-size: 12px;
    display: none;
}

.macros-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.macro-item {
    text-align: center;
    padding: 15px;
    background: rgba(255,255,255,0.8);
    border-radius: 8px;
    border: 1px solid var(--nevada);
}

.macro-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.macro-info h4 {
    font-size: 14px;
    color: var(--black-pearl);
    margin-bottom: 5px;
    font-weight: 600;
}

.macro-amount {
    font-size: 18px;
    font-weight: 700;
    color: var(--black-pearl);
    margin-bottom: 3px;
}

.macro-percent {
    font-size: 11px;
    color: var(--nevada);
    margin: 0;
}

.macro-explanation {
    background: rgba(255,255,255,0.6);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--nevada);
}

.macro-explanation p {
    margin: 5px 0;
    font-size: 13px;
    color: var(--black-pearl);
    line-height: 1.4;
}

.macro-explanation p:first-child {
    font-weight: 600;
    margin-bottom: 10px;
}

/* Right Sidebar */
.calculator-right-sidebar {
    width: 250px;
    background-color: var(--loblolly);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    flex-shrink: 0;
}

.sidebar-section {
    margin-bottom: 30px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--black-pearl);
    border-bottom: 1px solid var(--nevada);
    padding-bottom: 5px;
}

/* Sources Section */
.sources-section {
    background: var(--loblolly);
    padding: 40px 0;
    margin-top: 40px;
}

.sources-layout {
    display: flex;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.sources-left,
.sources-right {
    width: 250px;
    background-color: var(--white);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    flex-shrink: 0;
}

.sources-center {
    flex: 1;
    background-color: var(--white);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.sources-header {
    text-align: center;
    margin-bottom: 30px;
}

.sources-header h1 {
    color: var(--black-pearl);
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.sources-header p {
    color: var(--nevada);
    margin: 8px 0 0 0;
    font-size: 14px;
}

.sources-title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--black-pearl);
}

.source-item {
    margin-bottom: 16px;
}

.source-item:last-child {
    margin-bottom: 0;
}

.source-subtitle {
    font-size: 14px;
    font-weight: 600;
    color: var(--black-pearl);
    margin-bottom: 6px;
}

.source-text {
    font-size: 13px;
    color: var(--nevada);
    line-height: 1.5;
    margin: 0;
}

.guidelines-section,
.reliability-section {
    margin-bottom: 25px;
}

.guidelines-section:last-child,
.reliability-section:last-child {
    margin-bottom: 0;
}

.guidelines-title,
.reliability-title {
    color: var(--black-pearl);
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.guidelines-intro,
.reliability-text {
    color: var(--nevada);
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.6;
}

.guidelines-list {
    display: grid;
    gap: 8px;
}

.guideline-item {
    color: var(--black-pearl);
    font-size: 14px;
    line-height: 1.5;
}

/* BMI Indicator (hidden but needed for JS compatibility) */
.bmi-indicator {
    display: none;
}

.bmi-placeholder {
    /* Hidden placeholder for BMI functionality */
    display: none;
}

/* Main Result Display */
.main-result {
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 2px solid var(--granny-smith);
    display: none;
    transition: all 0.3s ease-in-out;
}

.main-result.show {
    display: block;
}

.result-label {
    font-size: 16px;
    color: var(--nevada);
    margin-bottom: 8px;
    font-weight: 600;
}

.target-calories {
    font-size: 32px;
    font-weight: 700;
    color: var(--black-pearl);
    margin-bottom: 8px;
}

.goal-text {
    font-size: 14px;
    color: var(--nevada);
    font-style: italic;
}

/* BMR/TDEE Results */
.bmr-tdee-results {
    display: none;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
    transition: all 0.3s ease-in-out;
}

.bmr-tdee-results.show {
    display: grid;
}

.result-box {
    text-align: center;
    padding: 15px;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
    border: 1px solid var(--nevada);
}

.result-title {
    font-size: 12px;
    color: var(--black-pearl);
    font-weight: 600;
    margin-bottom: 4px;
}

.result-subtitle {
    font-size: 10px;
    color: var(--nevada);
    margin-bottom: 6px;
}

.result-value {
    font-size: 16px;
    font-weight: 700;
    color: var(--black-pearl);
}

/* Macros Container */
.macros-container {
    margin-bottom: 20px;
    font-size: 12px;
    display: none;
    transition: all 0.3s ease-in-out;
}

.macros-container.show {
    display: block;
}

/* Form Section */
.calculator-form-section {
    background: rgba(255,255,255,0.5);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid var(--nevada);
    margin-bottom: 20px;
}



.form-title {
    color: var(--black-pearl);
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
}

.calculator-form {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
}

.form-fieldset {
    grid-column: 1 / -1;
    border: 1px solid var(--nevada);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: rgba(255,255,255,0.3);
}

.form-legend {
    color: var(--black-pearl);
    font-weight: 700;
    font-size: 16px;
    padding: 0 8px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.form-grid-two {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 16px;
}

.form-field {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 13px;
    color: var(--black-pearl);
    margin-bottom: 6px;
    font-weight: 600;
}

.form-input {
    padding: 10px;
    border: 2px solid var(--nevada);
    border-radius: 6px;
    font-size: 14px;
    background: var(--white);
    transition: border-color 0.3s;
}

.form-input:focus {
    border-color: var(--granny-smith);
    outline: none;
    box-shadow: 0 0 0 3px rgba(101, 163, 13, 0.1);
}

.form-input:hover {
    border-color: var(--granny-smith);
}

/* BMI Indicator */
.bmi-indicator {
    display: none;
}

/* Right Sidebar */
.calculator-right-sidebar {
    width: 250px;
    background-color: var(--loblolly);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}

.sidebar-section {
    margin-bottom: 30px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--black-pearl);
    border-bottom: 1px solid var(--nevada);
    padding-bottom: 5px;
}

/* Sources Section */
.sources-section {
    background: var(--loblolly);
    padding: 40px 0;
    margin-top: 40px;
}

.sources-layout {
    display: flex;
    gap: 20px;
}

.sources-left,
.sources-right {
    width: 250px;
    background-color: var(--white);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    flex-shrink: 0;
}

.sources-center {
    flex: 1;
    background-color: var(--white);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.sources-title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--black-pearl);
}

.source-item {
    margin-bottom: 16px;
}

.source-item:last-child {
    margin-bottom: 0;
}

.source-subtitle {
    font-size: 14px;
    font-weight: 600;
    color: var(--black-pearl);
    margin-bottom: 6px;
}

.source-text {
    font-size: 13px;
    color: var(--nevada);
    line-height: 1.5;
    margin: 0;
}

.source-text strong {
    color: var(--black-pearl);
}

/* Sources Header */
.sources-header {
    text-align: center;
    margin-bottom: 30px;
}

.sources-header h1 {
    color: var(--black-pearl);
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.sources-header p {
    color: var(--nevada);
    margin: 8px 0 0 0;
    font-size: 14px;
}

/* Guidelines Section */
.guidelines-section {
    margin-bottom: 25px;
}

.guidelines-title {
    color: var(--black-pearl);
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.guidelines-intro {
    color: var(--nevada);
    margin-bottom: 12px;
    font-size: 14px;
}

.guidelines-list {
    display: grid;
    gap: 8px;
}

.guideline-item {
    color: var(--black-pearl);
    font-size: 14px;
    line-height: 1.5;
}

.guideline-item strong {
    font-weight: 600;
}

/* Reliability Section */
.reliability-title {
    color: var(--black-pearl);
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.reliability-text {
    color: var(--nevada);
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
}

.reliability-text strong {
    color: var(--black-pearl);
}

/* ===== FOOTER BASE STYLES ===== */
.site-footer {
    margin-top: 30px;
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow-x: hidden;
}

.footer-wrapper {
    background: var(--loblolly);
    border-top: 1px solid var(--nevada);
    padding: 40px 0 20px 0;
    width: 100%;
    margin: 0;
    box-sizing: border-box;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.5fr 1.5fr;
    grid-template-rows: auto auto;
    gap: 30px 40px;
    margin-bottom: 30px;
    width: 100%;
    box-sizing: border-box;
}

/* ROW 1 - Desktop Grid Layout */
.footer-brand {
    grid-column: 1;
    grid-row: 1;
}

.footer-nav-col {
    grid-column: 2;
    grid-row: 1;
}

.footer-legal-col {
    grid-column: 3;
    grid-row: 1;
}

.footer-contact-col {
    grid-column: 4;
    grid-row: 1;
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 25px;
    align-self: start;
}

/* ROW 2 - Desktop Grid Layout */
.footer-section.affiliate-disclaimer {
    grid-column: 1;
    grid-row: 2;
    align-self: start;
}

.footer-section.newsletter-section {
    grid-column: 2 / 4;
    grid-row: 2;
    text-align: center;
    display: flex;
    justify-content: center;
    align-self: start;
}

.footer-section.business-info-section {
    grid-column: 4;
    grid-row: 2;
    text-align: left;
    align-self: start;
}

/* ===== FOOTER SECTIONS ===== */
.footer-section {
    /* Ensure all grid items have consistent behavior */
    min-height: 0;
    overflow: hidden;
    align-self: start;
}

/* ===== BRAND SECTION ===== */
.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.brand-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.brand-title {
    color: var(--black-pearl);
    font-size: 20px;
    font-weight: 700;
    margin: 0;
}

.brand-badge {
    background: var(--granny-smith);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    margin-left: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.brand-description {
    color: var(--nevada);
    line-height: 1.4;
    margin-bottom: 0px;
    font-size: 13px;
}

.trust-indicators {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: 12px;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.trust-icon {
    color: var(--granny-smith);
    font-weight: bold;
    font-size: 14px;
}

.trust-text {
    color: var(--nevada);
    font-size: 12px;
    font-weight: 600;
}

/* ===== FOOTER COLUMNS ===== */
.footer-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-title {
    color: var(--black-pearl);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
    margin-top: 0;
}

.footer-contact-col .footer-title {
    text-align: left;
}

.footer-nav {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.footer-link {
    color: var(--black-pearl);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    line-height: 1.5;
}

.footer-link.secondary {
    color: var(--nevada);
}

.footer-link:hover {
    color: var(--granny-smith);
    transform: translateX(3px);
}

.link-arrow {
    margin-right: 8px;
    color: var(--nevada);
    transition: color 0.2s ease;
}

.footer-link:hover .link-arrow {
    color: var(--granny-smith);
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
    text-align: left;
}

.contact-email {
    color: var(--black-pearl);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    transition: all 0.2s ease;
}

.contact-email:hover {
    color: var(--granny-smith);
    transform: translateX(3px);
}

.email-icon {
    font-size: 16px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    text-align: left;
}

.contact-item {
    color: var(--nevada);
    font-size: 12px;
    line-height: 1.4;
}

.contact-item strong {
    color: var(--black-pearl);
    font-weight: 600;
}

/* ===== CONTACT SECTION WRAPPERS ===== */
.contact-section-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.business-info-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
    text-align: left;
}

/* ===== BUSINESS INFO ===== */
.business-title {
    color: var(--black-pearl);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
}

.business-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.business-details div {
    color: var(--nevada);
    font-size: 12px;
    line-height: 1.4;
}

/* ===== AFFILIATE DISCLAIMER ===== */
.affiliate-disclaimer {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.disclaimer-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.disclaimer-icon {
    color: var(--granny-smith);
    font-size: 14px;
    margin-left: 6px;
}

.disclaimer-title {
    color: var(--black-pearl);
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    display: flex;
    align-items: center;
}

.disclaimer-description {
    color: var(--nevada);
    font-size: 11px;
    line-height: 1.4;
    margin: 0;
}

/* ===== NEWSLETTER SECTION ===== */
.newsletter-section {
    max-width: 400px;
    margin: 0 auto;
    width: 100%;
}

.newsletter-content {
    text-align: center;
    padding: 0 10px;
    box-sizing: border-box;
}

.newsletter-title {
    color: var(--black-pearl);
    font-size: 15px;
    font-weight: 600;
    margin: 0 0 3px 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.newsletter-icon {
    margin-left: 6px;
    font-size: 15px;
}

.newsletter-description {
    color: var(--nevada);
    font-size: 12px;
    line-height: 1.4;
    margin: 0 0 12px 0;
}

.newsletter-form {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    width: 100%;
    max-width: 380px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.newsletter-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--nevada);
    border-radius: 4px;
    font-size: 12px;
    background: var(--white);
    color: var(--black-pearl);
    min-width: 0;
    box-sizing: border-box;
}

.newsletter-input:focus {
    outline: none;
    border-color: var(--granny-smith);
}

.newsletter-button {
    padding: 8px 16px;
    background: var(--granny-smith);
    color: var(--white);
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
    box-sizing: border-box;
}

.newsletter-button:hover {
    background: var(--black-pearl);
    transform: translateY(-1px);
}

.newsletter-note {
    color: var(--nevada);
    font-size: 11px;
    text-align: center;
    margin: 0;
}

/* ===== FOOTER BOTTOM ===== */
.footer-bottom {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding: 20px 0 0 0;
    margin-top: 30px;
    box-sizing: border-box;
}

.footer-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100vw;
    height: 1px;
    background: var(--nevada);
}

.copyright {
    color: var(--nevada);
    font-size: 13px;
}

.footer-meta {
    display: flex;
    align-items: center;
    gap: 20px;
}

.footer-copyright {
    color: var(--white);
    font-size: 13px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    text-align: left;
    width: auto;
    padding-left: calc(50vw - 600px + 40px + 320px);
    margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Layout */
@media (max-width: 1024px) {
    .footer-wrapper {
        padding: 35px 30px 20px 30px;
    }

    .footer-container {
        padding: 0;
        max-width: 100%;
    }

    .footer-grid {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: repeat(6, auto);
        gap: 25px 30px;
        width: 100%;
    }

    .footer-bottom {
        padding: 20px 0 0 0;
    }

    /* Row 1 - Brand spans full width */
    .footer-brand {
        grid-column: 1 / -1;
        grid-row: 1;
        text-align: left;
        margin-bottom: 10px;
        width: 100%;
        max-width: 100%;
    }

    /* Row 2-3 - Navigation columns */
    .footer-nav-col {
        grid-column: 1;
        grid-row: 2;
        width: 100%;
        max-width: 100%;
    }

    .footer-legal-col {
        grid-column: 2;
        grid-row: 2;
        width: 100%;
        max-width: 100%;
    }

    .footer-contact-col {
        grid-column: 1 / -1;
        grid-row: 3;
        width: 100%;
        max-width: 100%;
        text-align: left;
        display: block;
        gap: 20px;
    }

    /* Row 4-6 - Secondary sections */
    .footer-section.affiliate-disclaimer {
        grid-column: 1 / -1;
        grid-row: 4;
        width: 100%;
        max-width: 100%;
    }

    .footer-section.newsletter-section {
        grid-column: 1 / -1;
        grid-row: 5;
        width: 100%;
        max-width: 100%;
        text-align: center;
    }

    .footer-section.business-info-section {
        grid-column: 1 / -1;
        grid-row: 6;
        width: 100%;
        max-width: 100%;
        text-align: left;
    }

    /* Reset text alignment for tablet */
    .contact-section {
        text-align: left;
        align-items: flex-start;
    }

    .contact-email {
        justify-content: flex-start;
    }

    .contact-info {
        text-align: left;
    }

    .business-info-wrapper {
        text-align: left;
        margin-top: 20px;
    }

    .business-details {
        text-align: left;
    }
}

/* Mobile Layout - Stacked Columns */
@media (max-width: 768px) {
    .footer-wrapper {
        padding: 25px 20px 30px 20px;
        width: 100%;
        margin: 0;
        box-sizing: border-box;
    }

    .footer-container {
        padding: 0;
        max-width: 100%;
        box-sizing: border-box;
    }

    .footer-grid {
        display: flex;
        flex-direction: column;
        gap: 25px;
        width: 100%;
        box-sizing: border-box;
    }

    .footer-bottom {
        padding: 20px 0 15px 0;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-top: 25px;
        box-sizing: border-box;
    }

    /* All elements stack vertically */
    .footer-brand,
    .footer-column,
    .footer-section {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Mobile-specific alignment for each section */
    .footer-brand {
        text-align: left;
        margin-bottom: 0;
        padding: 0 10px;
    }

    .footer-nav-col,
    .footer-legal-col {
        text-align: left;
        padding: 0 10px;
    }

    .footer-contact-col {
        text-align: left;
        display: block;
        gap: 25px;
        padding: 0 10px;
    }

    /* Secondary sections with consistent padding */
    .footer-section.affiliate-disclaimer {
        text-align: left;
        padding: 0 10px;
    }

    .footer-section.newsletter-section {
        text-align: left;
        padding: 0 10px;
        justify-content: flex-start;
    }

    .footer-section.business-info-section {
        text-align: left;
        padding: 0 10px;
    }

    .contact-section {
        text-align: left;
        align-items: flex-start;
    }

    .contact-email {
        justify-content: flex-start;
    }

    .contact-info {
        text-align: left;
    }

    .business-info-wrapper {
        text-align: left;
        margin-top: 0;
    }

    .business-details {
        text-align: left;
    }

    .footer-section.footer-empty {
        display: none;
    }

    .footer-copyright {
        padding: 0 20px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        text-align: center;
        width: 100%;
        box-sizing: border-box;
        padding-left: 20px;
    }

    .newsletter-form {
        flex-direction: column;
        gap: 10px;
        width: 100%;
        box-sizing: border-box;
        max-width: 300px;
        margin: 0;
    }

    .newsletter-input,
    .newsletter-button {
        width: 100%;
        box-sizing: border-box;
        max-width: 100%;
    }

    .newsletter-section {
        max-width: 100%;
        width: 100%;
        box-sizing: border-box;
        margin: 0;
    }

    .newsletter-content {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        text-align: left;
        padding: 0;
    }

    .newsletter-note {
        text-align: left;
    }

    .newsletter-title {
        justify-content: flex-start;
    }

    /* Ensure all text elements don't overflow */
    .brand-description,
    .disclaimer-description,
    .newsletter-description,
    .footer-link,
    .contact-item {
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .footer-wrapper {
        padding: 20px 15px 25px 15px;
        box-sizing: border-box;
    }

    .footer-container {
        padding: 0;
        box-sizing: border-box;
    }

    .footer-grid {
        gap: 20px;
        box-sizing: border-box;
    }

    .footer-bottom {
        padding: 20px 0 15px 0;
    }

    /* Adjust padding for smaller screens */
    .footer-brand,
    .footer-nav-col,
    .footer-legal-col,
    .footer-contact-col,
    .footer-section.affiliate-disclaimer,
    .footer-section.newsletter-section,
    .footer-section.business-info-section {
        padding: 0 5px;
    }

    .brand-title {
        font-size: 18px;
    }

    .footer-title {
        font-size: 15px;
    }

    .brand-description {
        font-size: 13px;
    }

    .footer-link {
        font-size: 13px;
    }

    .trust-indicators {
        flex-direction: column;
        gap: 4px;
    }

    .trust-item {
        justify-content: flex-start;
    }

    .newsletter-form {
        gap: 8px;
        width: 100%;
        box-sizing: border-box;
    }

    .newsletter-input,
    .newsletter-button {
        padding: 10px 12px;
        font-size: 14px;
        width: 100%;
        box-sizing: border-box;
    }
}

/* Extra Small Mobile */
@media (max-width: 360px) {
    .footer-wrapper {
        padding: 15px 10px 20px 10px;
        box-sizing: border-box;
    }

    .footer-container {
        padding: 0;
        box-sizing: border-box;
    }

    .footer-grid {
        gap: 15px;
        box-sizing: border-box;
    }

    .footer-bottom {
        padding: 18px 0 12px 0;
    }

    /* Extra small padding adjustments */
    .footer-brand,
    .footer-nav-col,
    .footer-legal-col,
    .footer-contact-col,
    .footer-section.affiliate-disclaimer,
    .footer-section.newsletter-section,
    .footer-section.business-info-section {
        padding: 0 2px;
    }

    .footer-copyright {
        font-size: 12px;
        padding: 0;
        box-sizing: border-box;
    }

    .newsletter-input,
    .newsletter-button {
        font-size: 13px;
        padding: 8px 10px;
    }
}

/* Print styles */
@media print {
    .site-footer {
        display: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .site-footer {
        border-top-width: 3px;
    }

    .footer-link:hover {
        text-decoration: underline;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .footer-link,
    .social-link,
    .newsletter-button,
    .contact-email {
        transition: none;
    }

    .footer-link:hover,
    .social-link:hover,
    .newsletter-button:hover,
    .contact-email:hover {
        transform: none;
    }
}

/* ===== BLOG PAGE STYLES ===== */

/* Hero Section - Match index page styling */
.blog-hero {
    background: linear-gradient(to right, var(--black-pearl), var(--river-bed-alt));
    padding: 80px 20px 60px;
    text-align: center;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    position: relative;
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
    padding: 40px;
}

.blog-hero h1 {
    color: var(--white);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-description {
    color: var(--loblolly);
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 0;
}

/* Main Container */
.blog-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* Sidebar Styles */
.blog-sidebar {
    background: var(--white);
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 16px rgba(6, 32, 52, 0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.sidebar-section {
    margin-bottom: 30px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-section h3 {
    color: var(--black-pearl);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    border-bottom: 2px solid var(--loblolly);
    padding-bottom: 8px;
}

/* Search Styles */
.search-container {
    display: flex;
    gap: 8px;
}

.search-input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid var(--loblolly);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--granny-smith);
}

.search-button {
    background: var(--button-color);
    color: var(--white);
    border: none;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-button:hover {
    background: var(--hover-button-color);
}

/* Filter Checkboxes */
.category-filters {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.filter-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.95rem;
    color: var(--river-bed);
    transition: color 0.3s ease;
}

.filter-checkbox:hover {
    color: var(--black-pearl);
}

.filter-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--loblolly);
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.filter-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--button-color);
    border-color: var(--button-color);
}

.filter-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

/* Sort Dropdown */
.sort-dropdown {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--loblolly);
    border-radius: 8px;
    background: var(--white);
    font-size: 0.95rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.sort-dropdown:focus {
    outline: none;
    border-color: var(--granny-smith);
}

/* Category Links */
.category-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.category-link {
    display: block;
    padding: 8px 12px;
    color: var(--river-bed);
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.category-link:hover {
    background: var(--loblolly);
    color: var(--black-pearl);
}

.category-link.active {
    background: var(--granny-smith);
    color: var(--white);
    font-weight: 600;
}

/* Sidebar CTA */
.sidebar-cta {
    background: linear-gradient(135deg, var(--loblolly) 0%, var(--granny-smith) 100%);
    border-radius: 12px;
    padding: 25px;
    margin-top: 30px;
}

.cta-item {
    margin-bottom: 25px;
}

.cta-item:last-child {
    margin-bottom: 0;
}

.cta-item h4 {
    color: var(--black-pearl);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.cta-item p {
    color: var(--river-bed);
    font-size: 0.9rem;
    margin-bottom: 12px;
    line-height: 1.5;
}

.cta-button {
    display: inline-block;
    background: var(--button-color);
    color: var(--white);
    padding: 10px 16px;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background: var(--hover-button-color);
    transform: translateY(-1px);
}

.cta-button.secondary {
    background: var(--nevada);
}

.cta-button.secondary:hover {
    background: var(--river-bed);
}

/* Main Content */
.blog-main {
    min-height: 600px;
}

.blog-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--loblolly);
}

.results-info {
    color: var(--nevada);
    font-weight: 600;
}

/* Blog Grid */
.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
    transform: translateZ(0); /* Prevent layout thrashing */
}

.blog-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(6, 32, 52, 0.1);
    cursor: pointer;
}

.blog-card:hover {
    box-shadow: 0 6px 20px rgba(6, 32, 52, 0.12);
}

.blog-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--loblolly);
}

.blog-content {
    padding: 25px;
}

.blog-category {
    display: inline-block;
    background: var(--loblolly);
    color: var(--river-bed);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 12px;
}

.blog-title {
    color: var(--black-pearl);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 12px;
    line-height: 1.4;
}

.blog-summary {
    color: var(--nevada);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.blog-date {
    color: var(--granny-smith);
    font-size: 0.85rem;
    font-weight: 600;
}

.blog-read-time {
    color: var(--granny-smith);
    font-size: 0.85rem;
}

.read-more-btn {
    background: var(--button-color);
    color: var(--white);
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.read-more-btn:hover {
    background: var(--hover-button-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 40px;
}

.pagination-btn {
    background: var(--button-color);
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--hover-button-color);
}

.pagination-btn:disabled {
    background: var(--loblolly);
    color: var(--nevada);
    cursor: not-allowed;
}

.pagination-info {
    color: var(--nevada);
    font-weight: 600;
}

/* Newsletter CTA Section */
.newsletter-cta-section {
    background: linear-gradient(135deg, var(--black-pearl) 0%, var(--river-bed) 100%);
    padding: 60px 20px;
    text-align: center;
    margin-top: 60px;
}

.newsletter-cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-cta-section h2 {
    color: var(--white);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.newsletter-cta-section p {
    color: var(--loblolly);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
}

.newsletter-form {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
}

.newsletter-button {
    background: var(--button-color);
    color: var(--white);
    border: none;
    padding: 15px 25px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.newsletter-button:hover {
    background: var(--granny-smith);
}

.newsletter-benefits {
    display: flex;
    justify-content: center;
    gap: 20px;
    color: var(--loblolly);
    font-size: 0.9rem;
}

/* Blog Mobile Responsive */
@media (max-width: 768px) {
    .blog-container {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px 15px;
    }

    .blog-sidebar {
        order: 2;
        position: static;
        padding: 20px;
    }

    .blog-main {
        order: 1;
    }

    .blog-hero {
        padding: 60px 20px 40px;
    }

    .hero-content {
        padding: 30px 20px;
    }

    .blog-hero h1 {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .blog-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .newsletter-form {
        flex-direction: column;
        gap: 12px;
    }

    .newsletter-benefits {
        flex-direction: column;
        gap: 8px;
    }

    .sidebar-cta {
        margin-top: 20px;
    }
}

/* ===== BLOG POST PAGE STYLES ===== */

.blog-post-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.blog-post-content {
    background: var(--white);
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 4px 16px rgba(6, 32, 52, 0.1);
}

.breadcrumb {
    color: var(--nevada);
    font-size: 0.9rem;
    margin-bottom: 30px;
}

.breadcrumb a {
    color: var(--granny-smith);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: var(--black-pearl);
}

.article-header {
    margin-bottom: 40px;
}

.article-category {
    display: inline-block;
    background: var(--loblolly);
    color: var(--river-bed);
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.article-header h1 {
    color: var(--black-pearl);
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 15px;
}

.article-meta {
    display: flex;
    gap: 20px;
    color: var(--granny-smith);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 30px;
}

.article-hero-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 12px;
}

.article-content {
    line-height: 1.8;
}

.article-intro {
    font-size: 1.1rem;
    color: var(--river-bed);
    font-style: italic;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--loblolly);
    border-radius: 8px;
    border-left: 4px solid var(--granny-smith);
}

.article-body h2 {
    color: var(--black-pearl);
    font-size: 1.8rem;
    font-weight: 700;
    margin: 30px 0 15px 0;
}

.article-body h3 {
    color: var(--black-pearl);
    font-size: 1.4rem;
    font-weight: 600;
    margin: 25px 0 12px 0;
}

.article-body p {
    color: var(--nevada);
    margin-bottom: 20px;
}

.article-body ul, .article-body ol {
    color: var(--nevada);
    margin-bottom: 20px;
    padding-left: 25px;
}

.article-body li {
    margin-bottom: 8px;
}

.related-articles {
    margin-top: 50px;
    padding-top: 30px;
    border-top: 2px solid var(--loblolly);
}

.related-articles h2 {
    color: var(--black-pearl);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.related-article {
    background: var(--loblolly);
    border-radius: 8px;
    padding: 20px;
    transition: transform 0.3s ease;
}

.related-article:hover {
    transform: translateY(-2px);
}

.related-article img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 12px;
}

.related-article h4 {
    color: var(--black-pearl);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.related-article p {
    color: var(--nevada);
    font-size: 0.9rem;
    margin-bottom: 12px;
}

.related-article a {
    color: var(--granny-smith);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.related-article a:hover {
    color: var(--black-pearl);
}

.back-to-blog {
    margin-top: 40px;
    text-align: center;
}

.back-button {
    display: inline-block;
    background: var(--button-color);
    color: var(--white);
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.back-button:hover {
    background: var(--hover-button-color);
    transform: translateY(-1px);
}

/* Blog Post Sidebar */
.blog-post-sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.blog-post-sidebar .sidebar-section {
    background: var(--white);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 16px rgba(6, 32, 52, 0.1);
}

.blog-post-sidebar .sidebar-section h3 {
    color: var(--black-pearl);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    border-bottom: 2px solid var(--loblolly);
    padding-bottom: 8px;
}

.blog-post-sidebar .sidebar-section p {
    color: var(--nevada);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.newsletter-signup {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.newsletter-signup .newsletter-input {
    padding: 10px 12px;
    border: 2px solid var(--loblolly);
    border-radius: 6px;
    font-size: 0.9rem;
}

.newsletter-signup .newsletter-button {
    background: var(--button-color);
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.newsletter-signup .newsletter-button:hover {
    background: var(--hover-button-color);
}

.related-supplements {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.supplement-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 12px;
    background: var(--loblolly);
    border-radius: 6px;
    text-decoration: none;
    color: var(--river-bed);
    font-weight: 600;
    transition: all 0.3s ease;
}

.supplement-link:hover {
    background: var(--granny-smith);
    color: var(--white);
    transform: translateX(5px);
}

.supplement-icon {
    font-size: 1.2rem;
}

.popular-articles {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.popular-article {
    padding: 10px 0;
    border-bottom: 1px solid var(--loblolly);
}

.popular-article:last-child {
    border-bottom: none;
}

.popular-article a {
    color: var(--river-bed);
    text-decoration: none;
    font-size: 0.9rem;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.popular-article a:hover {
    color: var(--black-pearl);
}

/* Blog Post Mobile Responsive */
@media (max-width: 768px) {
    .blog-post-container {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px 15px;
    }

    .blog-post-content {
        padding: 25px 20px;
    }

    .article-header h1 {
        font-size: 2rem;
    }

    .article-meta {
        flex-direction: column;
        gap: 8px;
    }

    .related-grid {
        grid-template-columns: 1fr;
    }

    .blog-post-sidebar {
        order: -1;
    }
}
