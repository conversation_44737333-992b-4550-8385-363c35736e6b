:root {
    --black-pearl: #062034;
    --loblolly: #c5d1d5;
    --nevada: #64767e;
    --granny-smith: #7b949a;
    --river-bed: #495d62;
    --river-bed-alt: #44535c;
    --white: #ffffff;
    --button-color: #7b949a;
    --hover-button-color: #062034;
}

body {
    font-family: 'Open Sans', sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--white);
    color: var(--black-pearl);
    line-height: 1.8;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background-color: var(--white);
    padding: 15px 35px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1000;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
}

.logo-container img {
    max-width: 110px;
    margin-right: 15px;
}

.logo-container h1 {
    font-size: 30px;
    color: var(--black-pearl);
    margin: 0;
    font-weight: 700;
    line-height: 1.2;
}

.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-menu a {
    color: var(--black-pearl);
    text-decoration: none;
    font-weight: 700;
    font-size: 16px;
    transition: color 0.3s ease, font-size 0.3s ease;
}

.nav-menu a:hover {
    color: var(--button-color);
    font-size: 18px;
}

.nav-menu a.active {
    color: var(--button-color);
}

.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
    padding: 10px 0;
    border-radius: 5px;
    z-index: 1000;
}

.dropdown-content a {
    display: block;
    padding: 10px 20px;
    color: var(--black-pearl);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.dropdown-content a:hover {
    background-color: var(--loblolly);
    color: var(--white);
}

.dropdown:hover .dropdown-content {
    display: block;
}

.content-container {
    display: flex;
    gap: 20px;
    padding: 20px;
    align-items: stretch;
}

.filter-menu {
    flex: 0 0 300px;
    background-color: var(--loblolly);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.filter-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-section h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--black-pearl);
    border-bottom: 1px solid var(--nevada);
    padding-bottom: 5px;
}

.categories ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.categories ul li {
    display: flex;
    align-items: center;
}

.categories ul li input[type="checkbox"] {
    margin-right: 10px;
    accent-color: var(--button-color);
    transform: scale(1.2);
}

.categories ul li span {
    font-size: 14px;
    color: var(--nevada);
}

.price-filter {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.price-range {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.price-range input[type="number"] {
    width: 60px;
    padding: 5px;
    border: 1px solid var(--nevada);
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
}

.price-range span {
    font-size: 18px;
    font-weight: 700;
    color: var(--black-pearl);
}

.rating-filter {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.rating-filter label {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-filter input[type="radio"] {
    accent-color: var(--button-color);
    transform: scale(1.2);
}

.rating-filter .stars {
    font-size: 14px;
    color: var(--granny-smith);
}

.product-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 0;
    background-color: var(--white);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.product-table th,
.product-table td {
    padding: 15px;
    text-align: center;
    border: 1px solid var(--nevada);
}

.product-table th {
    background-color: var(--loblolly);
    font-size: 16px;
    font-weight: 700;
    color: var(--black-pearl);
}

.product-table td {
    font-size: 14px;
    color: var(--black-pearl);
}

.product-table img {
    width: 100%;
    height: 100%;
    max-width: 260px;
    max-height: 260px;
    object-fit: cover;
    border-radius: 5px;
}

.product-table .button {
    padding: 10px 15px;
    background: var(--button-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.product-table .button:hover {
    background: var(--hover-button-color);
}

.footer {
    background-color: var(--river-bed-alt);
    padding: 20px;
    text-align: center;
    color: var(--white);
}

.footer-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
    max-width: 1080px;
    margin: 0 auto;
}

.footer-column {
    flex: 1;
    min-width: 200px;
    color: var(--loblolly);
}

.footer-column h2 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--white);
}

.footer-column ul {
    list-style: none;
    padding: 0;
}

.footer-column ul li a {
    color: var(--white);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column ul li a:hover {
    color: var(--button-color);
}

.footer-bottom {
    margin-top: 20px;
    font-size: 14px;
    color: var(--loblolly);
}

.social-icons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.social-icons img {
    width: 30px;
    height: 30px;
    transition: transform 0.3s ease;
}

.social-icons img:hover {
    transform: scale(1.1);
}

/* ===== FILTER SECTIONS STYLING ===== */
.filter-section {
    margin-bottom: 20px;
}

.filter-section h3 {
    margin-bottom: 10px;
    font-size: 1.1em;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.filter-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-section ul li {
    margin-bottom: 8px;
}

/* ===== PRIJSVIERKANTEN EN INPUTVELDEN ===== */
.price-boxes {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.price-box input[type="number"] {
    width: 50px;
    border: none;
    outline: none;
    text-align: right;
    font-size: 1em;
    font-weight: bold;
    background: #fff;
    padding: 0;
}

/* Pijltjes zichtbaar maken voor Chrome/Safari */
.price-box input[type="number"]::-webkit-inner-spin-button,
.price-box input[type="number"]::-webkit-outer-spin-button {
    opacity: 1;
    margin: 0;
    cursor: pointer;
}

/* Hidden range inputs for price slider functionality */
#min-price-range,
#max-price-range {
    display: none;
}

/* Euroteken verwijderd (titel toont nu "Prijs (€)") */
#min-price-box span.currency,
#max-price-box span.currency {
    display: none;
}

#min-price-box,
#max-price-box {
    width: 60px;
    height: 60px;
    border: 1px solid #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    font-weight: bold;
}

/* ===== SLIDER CONTAINER EN TRACK ===== */
.price-slider-container {
    position: relative;
    height: 30px;
}

.price-slider-track {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: #ccc;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 1;
}

.slider-handle {
    position: absolute;
    top: 50%;
    width: 20px;
    height: 20px;
    background: #333;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 100;
    user-select: none;
    touch-action: none;
}

.reset-smooth {
    background-color: #ffdddd;
    transition: background-color 0.5s ease;
}

/* ===== RESULTATEN EN SORTERING ===== */
.results-sorting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.results-info {
    font-size: 1em;
    font-weight: bold;
}

.sort-menu {
    display: flex;
    align-items: center;
}

/* ===== STERREN VOOR BEOORDELING ===== */
.star {
    font-size: 16px;
    vertical-align: middle;
    margin: 0 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.filled-star {
    color: #F05D23;
    text-shadow: 0 1px 2px rgba(240, 93, 35, 0.3);
}

.empty-star {
    color: #999;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* ===== "BEKIJK MEER" KNOP ===== */
.button {
    white-space: nowrap;
}

/* ===== FILTER HEADER ===== */
.category-header {
    margin-bottom: 15px;
    border-bottom: 2px solid var(--loblolly);
    padding-bottom: 8px;
}

/* ===== ACTIVE FILTERS SECTION ===== */
.active-filters-section {
    margin-bottom: 20px;
    display: none; /* Hidden by default */
}

.active-filters-list {
    margin-bottom: 15px;
}

.active-filter-item {
    display: inline-block;
    background: var(--granny-smith);
    color: var(--white);
    padding: 6px 12px;
    margin: 4px 8px 4px 0;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.reset-filters-btn {
    background: var(--nevada);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    width: 100%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.reset-filters-btn:hover {
    background: var(--black-pearl);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}



/* Hide all toggle buttons completely */
.top-toggle,
.sidebar-toggle,
.filter-toggle {
    display: none !important;
}

/* Content container */
.content-container {
    display: flex;
    gap: 20px;
}

/* ===== FILTER SECTIONS STYLING ===== */
.filter-section {
    margin-bottom: 20px;
}

.filter-section h3 {
    margin-bottom: 10px;
    font-size: 1.1em;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.filter-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-section ul li {
    margin-bottom: 8px;
}





/* Mobile filter overlay */
.filter-menu.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw !important;
    height: 100vh !important;
    background: var(--white);
    z-index: 9999;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.filter-menu.mobile-overlay .filter-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 80px; /* Space for fixed button */
}

.mobile-filter-apply {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: var(--loblolly);
    padding: 12px 20px;
    margin: 0;
    z-index: 10002;
}

.mobile-filter-apply button {
    width: 100%;
    padding: 12px;
    font-size: 14px;
    font-weight: bold;
    background: var(--granny-smith);
    color: var(--white);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-filter-apply button:hover {
    background: var(--hover-button-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}



/* Content container aanpassingen */
.content-container {
    transition: all 0.3s ease;
    display: flex;
    gap: 20px;
}

.content-container.filters-hidden {
    display: block;
}

.content-container.filters-hidden .product-table-container {
    width: 100%;
    margin: 0;
}

/* ===== RESPONSIVE DESIGN - MOBILE FRIENDLY ===== */

/* Tablet and smaller screens */
@media (max-width: 1024px) {
    .header {
        padding: 10px 20px;
    }

    .logo-container img {
        max-width: 80px;
    }

    .nav-menu {
        gap: 15px;
    }

    .content-container {
        gap: 15px;
        padding: 0 15px;
    }

    .filter-menu {
        min-width: 200px;
    }
}

/* Mobile hamburger menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 20px;
    height: 2px;
    background-color: var(--black-pearl);
    margin: 2px 0;
    transition: 0.3s;
    border-radius: 1px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-4px, 4px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-4px, -4px);
}

/* Mobile screens */
@media (max-width: 768px) {
    /* Header responsive - more compact */
    .header {
        padding: 10px 15px;
        text-align: center;
        position: relative;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .logo-container {
        margin-bottom: 0;
        margin-left: 50px; /* Space for hamburger menu */
    }

    .logo-container img {
        max-width: 50px;
        margin-right: 8px;
    }

    .logo-container h1 {
        font-size: 1.2em;
        margin: 0;
    }

    /* Mobile navigation */
    .nav-menu {
        position: absolute;
        top: 100%;
        left: 15px;
        right: 15px;
        background: var(--white);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        flex-direction: column;
        padding: 10px;
        border-radius: 8px;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        max-width: 250px;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-menu a {
        padding: 8px 12px;
        margin: 3px 0;
        border-radius: 5px;
        background: var(--loblolly);
        color: var(--black-pearl);
        text-decoration: none;
        text-align: center;
        font-size: 14px;
    }

    .nav-menu a:hover,
    .nav-menu a.active {
        background: var(--granny-smith);
        color: var(--white);
    }

    /* Content layout mobile - horizontal with compact filters */
    .content-container {
        flex-direction: row;
        gap: 5px;
        padding: 8px;
        align-items: flex-start; /* Align to top */
    }

    /* Filter menu mobile - initially hidden, clean overlay when shown */
    .filter-menu {
        display: none !important; /* Initially completely hidden on mobile */
    }

    /* Mobile filter button above table */
    .mobile-filter-button-container {
        margin-bottom: 10px;
        width: 100%;
        display: none; /* Hidden by default, shown via JavaScript */
    }

    .mobile-filter-button {
        width: 100%;
        background: var(--granny-smith);
        color: var(--white);
        border: none;
        padding: 12px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s ease;
    }

    .mobile-filter-button:hover {
        background: #2d7a2d;
    }

    /* When filters are shown on mobile, use overlay */
    .filter-menu.mobile-overlay {
        display: flex !important;
        background: var(--loblolly);
        border: none;
        border-radius: 0;
        padding: 50px 8px 8px 8px; /* Extra top padding for close button */
    }

    /* Mobile filter close button (kruisje) */
    .mobile-filter-close {
        position: absolute;
        top: 15px;
        right: 15px;
        background: var(--granny-smith);
        color: var(--white);
        border: none;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }

    .mobile-filter-close:hover {
        background: #2d7a2d;
    }

    /* Clean mobile filter styling - extra compact with desktop-like background */
    .filter-menu.mobile-overlay .filter-section {
        margin: 8px 20px;
        background: var(--loblolly);
        border-radius: 0;
        padding: 8px;
        border: none;
    }

    .filter-menu.mobile-overlay .filter-section h3 {
        font-size: 16px;
        margin-bottom: 8px;
        color: var(--black-pearl);
        font-weight: bold;
    }

    /* Remove white background from price section specifically */
    .filter-menu.mobile-overlay .filter-section.price {
        background: transparent !important;
        padding: 0 !important;
        margin: 8px 20px;
        border: none !important;
    }

    .filter-menu.mobile-overlay .filter-section.price h3 {
        background: transparent !important;
        padding: 0 !important;
        margin-bottom: 8px;
        font-size: 16px;
        color: var(--black-pearl);
        font-weight: bold;
    }

    .filter-menu.mobile-overlay .filter-section ul li {
        margin-bottom: 4px;
        font-size: 14px;
    }

    .filter-menu.mobile-overlay .filter-section label {
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }

    .filter-menu.mobile-overlay .filter-section label:hover {
        background-color: var(--loblolly);
    }

    /* Mobile overlay active filters styling */
    .filter-menu.mobile-overlay .active-filters-section {
        margin-bottom: 20px;
        background: transparent;
        padding: 0;
    }

    .filter-menu.mobile-overlay .active-filters-list {
        margin-bottom: 15px;
    }

    .filter-menu.mobile-overlay .active-filter-item {
        display: inline-block;
        background: var(--granny-smith);
        color: var(--white);
        padding: 8px 12px;
        margin: 4px 8px 4px 0;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .filter-menu.mobile-overlay .reset-filters-btn {
        background: var(--nevada);
        color: var(--white);
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.2s ease;
        width: 100%;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .filter-menu.mobile-overlay .reset-filters-btn:hover {
        background: var(--black-pearl);
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    }

    .filter-menu.mobile-overlay .category-header {
        display: none; /* Hide the entire category header on mobile */
    }

    .filter-menu.mobile-overlay .category-header h3 {
        margin: 0;
        font-size: 11px;
        color: var(--black-pearl);
    }



    /* Mobile overlay price styling - same as desktop */
    .filter-menu.mobile-overlay .price-boxes {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        background: transparent !important;
        padding: 0 !important;
    }

    .filter-menu.mobile-overlay .price-box {
        flex: 1;
        padding: 0 !important;
        border: 1px solid #ccc;
        border-radius: 0;
        background: #fff !important;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 50px;
    }

    .filter-menu.mobile-overlay .price-box input[type="number"] {
        width: 50px;
        border: none;
        outline: none;
        text-align: right;
        font-size: 1em;
        font-weight: bold;
        background: #fff;
        padding: 0;
    }

    /* Pijltjes zichtbaar maken voor Chrome/Safari op mobile */
    .filter-menu.mobile-overlay .price-box input[type="number"]::-webkit-inner-spin-button,
    .filter-menu.mobile-overlay .price-box input[type="number"]::-webkit-outer-spin-button {
        opacity: 1;
        margin: 0;
        cursor: pointer;
    }

    .filter-menu.mobile-overlay .price-slider-container {
        height: 40px;
        margin: 15px 0;
        background: var(--loblolly);
        border-radius: 0;
        padding: 8px;
        border: none;
        position: relative;
    }

    .filter-menu.mobile-overlay .price-slider-track {
        height: 4px;
        background: #ccc;
        border-radius: 0;
        border: none;
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        pointer-events: none;
        z-index: 1;
    }

    .filter-menu.mobile-overlay .slider-handle {
        width: 20px;
        height: 20px;
        background: #333;
        border: none;
        box-shadow: none;
        border-radius: 50%;
        cursor: pointer;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 100;
        user-select: none;
        touch-action: none;
    }

    .product-table-container {
        flex: 1;
        overflow: auto;
        min-width: 0;
        width: 100%;
        padding: 0; /* No padding when filters are hidden */
    }



    /* Mobile filter sections are now handled by overlay styles above */

    /* Mobile price styling is now handled by overlay styles above */

    /* Mobile slider styling is now handled by overlay styles above */

    /* Table mobile - fully responsive and larger */
    .product-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin: 0;
        padding: 0;
        position: relative;
    }

    .product-table {
        width: 100%;
        min-width: 500px; /* Ensure minimum width for proper layout */
        font-size: 13px;
    }

    .product-table th,
    .product-table td {
        padding: 8px 6px;
        font-size: 13px;
        white-space: nowrap;
    }

    /* Hide less important columns on mobile but keep action column visible */
    .product-table th:nth-child(5), /* Details */
    .product-table td:nth-child(5) {
        display: none;
    }

    /* Ensure action column is always visible and prominent */
    .product-table th:nth-child(6) { /* Actie header */
        position: sticky;
        right: 0;
        background: var(--loblolly); /* Same as other headers */
        color: var(--black-pearl); /* Same as other headers */
        box-shadow: -2px 0 4px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .product-table td:nth-child(6) { /* Actie cells */
        position: sticky;
        right: 0;
        background: var(--white);
        box-shadow: -2px 0 4px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .product-table img {
        max-width: 100px; /* Larger and more prominent */
        max-height: 100px;
        border-radius: 6px; /* Modern rounded corners */
        box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Subtle shadow for prominence */
    }

    /* Optimized column widths for mobile - flexible height */
    .product-table {
        height: 100%; /* Fill container height */
        width: 100%;
    }

    .product-table th:nth-child(1), /* Product */
    .product-table td:nth-child(1) {
        width: 150px; /* Even larger for more prominent images */
    }

    .product-table th:nth-child(2), /* Naam */
    .product-table td:nth-child(2) {
        min-width: 100px; /* Smaller for product names */
    }

    .product-table th:nth-child(3), /* Prijs */
    .product-table td:nth-child(3) {
        width: 70px;
    }

    .product-table th:nth-child(4), /* Beoordeling */
    .product-table td:nth-child(4) {
        width: 90px;
    }

    .product-table th:nth-child(6), /* Actie */
    .product-table td:nth-child(6) {
        width: 90px;
    }

    /* Results sorting mobile - much smaller and left aligned */
    .results-sorting {
        flex-direction: row;
        gap: 10px;
        align-items: center;
        justify-content: flex-start; /* Left align */
        margin-bottom: 8px;
        padding: 3px 0;
    }

    .results-info {
        display: none; /* Hide results count on mobile */
    }

    .sort-menu {
        font-size: 10px; /* Smaller text */
        flex-shrink: 0;
    }

    .sort-menu span {
        display: none; /* Hide "Sortering:" text on mobile */
    }

    .sort-menu select {
        padding: 3px 5px;
        font-size: 10px; /* Smaller select */
        border-radius: 3px;
        border: 1px solid var(--nevada);
        background: var(--white);
        min-width: 120px; /* Smaller width */
        height: 24px; /* Smaller height */
    }

    /* Button mobile - more prominent */
    .button {
        padding: 8px 12px;
        font-size: 12px;
        background: var(--granny-smith);
        color: var(--white);
        border: none;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        min-width: 70px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .button:hover {
        background: var(--hover-button-color);
        transform: translateY(-1px);
        box-shadow: 0 3px 6px rgba(0,0,0,0.15);
    }

    /* Stars mobile - cleaner */
    .star {
        font-size: 14px;
        margin: 0;
    }
}

/* ===== INDEX PAGE SPECIFIC STYLING ===== */

/* Override content-container for index page - vertical layout */
body:not(.supplementen-page) .content-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    padding: 0; /* Remove padding to allow full width */
}

/* Main content wrapper for index page - gradient background */
body:not(.supplementen-page) .main-content-wrapper {
    background: linear-gradient(to right, var(--black-pearl), var(--river-bed-alt));
    padding: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    width: 100vw; /* Full viewport width */
    margin-left: calc(-50vw + 50%); /* Center and extend to full width */
}

body:not(.supplementen-page) .main-content {
    padding: 40px;
    max-width: 900px;
}

body:not(.supplementen-page) .main-content h1 {
    color: var(--white);
    font-size: 36px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
}

body:not(.supplementen-page) .main-content p {
    color: var(--white);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

body:not(.supplementen-page) .see-all-supplements {
    margin-top: 10px;
    padding: 12px 24px;
    background-color: var(--granny-smith);
    color: var(--white);
    font-weight: 700;
    text-decoration: none;
    border-radius: 5px;
    display: inline-block;
    transition: background-color 0.3s ease;
}

body:not(.supplementen-page) .see-all-supplements:hover {
    background-color: var(--black-pearl);
}

/* Categories section - overlapping style */
body:not(.supplementen-page) .categories-section {
    padding: 20px;
    margin-top: -20px; /* Subtle overlap with gradient section */
    background-color: var(--white);
    text-align: center;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

body:not(.supplementen-page) .see-categories {
    font-size: 28px;
    font-weight: 700;
    color: var(--black-pearl);
    margin-bottom: 20px;
}

body:not(.supplementen-page) .categories {
    display: flex;
    justify-content: space-around;
    max-width: 1080px;
    margin: 0 auto;
    gap: 20px;
}

body:not(.supplementen-page) .category-box {
    flex: 1;
    max-width: 240px;
    height: 240px;
    background-color: var(--loblolly);
    border-radius: 10px;
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease;
}

body:not(.supplementen-page) .category-box:hover {
    transform: scale(1.05);
}

body:not(.supplementen-page) .category-box a {
    display: block;
    position: relative;
    text-decoration: none;
}

body:not(.supplementen-page) .category-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

body:not(.supplementen-page) .category-box .see-more {
    position: absolute;
    bottom: 15px;
    right: 15px;
    padding: 8px 16px;
    background-color: var(--granny-smith);
    color: var(--white);
    font-weight: 700;
    text-decoration: none;
    border-radius: 5px;
    z-index: 2;
    transition: background-color 0.3s ease;
}

body:not(.supplementen-page) .category-box .see-more:hover {
    background-color: var(--black-pearl);
}



/* ===== CALORIE CALCULATOR PAGE STYLING ===== */

/* Calculator page layout */
body.calculator-page .content-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* DISABLE ALL CSS - USE ONLY INLINE STYLES */
.calculator-layout,
.calculator-layout .faq-sidebar,
.calculator-layout .calculator-section,
.calculator-layout .right-sidebar {
    /* All styles disabled - using inline styles only */
}

/* DISABLE MOBILE LAYOUT FOR NOW - TESTING */
@media (max-width: 1024px) {
    .calculator-layout {
        display: flex !important;
        flex-direction: column !important;
        gap: 20px !important;
    }

    .calculator-layout .faq-sidebar,
    .calculator-layout .calculator-section,
    .calculator-layout .right-sidebar {
        width: 100% !important;
        order: unset !important;
    }
}

/* FAQ Sidebar - DISABLED FOR TESTING */
.faq-sidebar {
    /* All styles disabled for testing */
}

.faq-sidebar h2 {
    font-size: 1.4em;
    margin-bottom: 25px;
    font-weight: 700;
    color: var(--nevada);
    line-height: 1.3;
}

/* Calculator section - DISABLED FOR TESTING */
.calculator-section {
    /* All styles disabled for testing */
}

.calculator-header {
    text-align: center;
    margin-bottom: 40px;
}

.calculator-header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    font-weight: 700;
    color: var(--nevada);
}

.calculator-header p {
    font-size: 1.1em;
    color: var(--nevada);
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.5;
}

/* BMI Indicator styling */
.bmi-indicator-container {
    margin: 30px 0;
    text-align: center;
}

.bmi-indicator-display {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 15px;
    padding: 30px;
    border: 2px solid #e8e8e8;
    transition: all 0.3s ease;
    min-height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bmi-placeholder {
    color: var(--nevada);
    opacity: 0.6;
}

.bmi-placeholder p {
    margin: 0;
    font-size: 0.9em;
}

/* BMI Scale Bar */
.bmi-scale {
    width: 100%;
    max-width: 500px;
    margin: 20px auto;
    position: relative;
}

.bmi-scale-bar {
    height: 40px;
    border-radius: 20px;
    background: linear-gradient(to right,
        #3498db 0%, #3498db 18.5%,
        #27ae60 18.5%, #27ae60 25%,
        #f39c12 25%, #f39c12 30%,
        #e74c3c 30%, #e74c3c 100%);
    position: relative;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.bmi-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 0.8em;
    color: var(--nevada);
}

.bmi-marker {
    position: absolute;
    top: -15px;
    width: 4px;
    height: 70px;
    background: #2c3e50;
    border-radius: 2px;
    transition: left 0.5s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.bmi-marker::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -6px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 12px solid #2c3e50;
}

.bmi-value-display {
    position: absolute;
    top: -45px;
    left: 50%;
    transform: translateX(-50%);
    background: #2c3e50;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 600;
    white-space: nowrap;
}

.bmi-category-display {
    text-align: center;
    margin-top: 15px;
    font-weight: 600;
    font-size: 1.1em;
}

/* Disclaimer Section */
.disclaimer-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 60px 0;
    margin-top: 80px;
    border-top: 3px solid var(--granny-smith);
}

.disclaimer-content h3 {
    text-align: center;
    color: var(--nevada);
    margin-bottom: 40px;
    font-size: 1.8em;
    font-weight: 700;
}

.disclaimer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.disclaimer-item {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border-left: 4px solid var(--granny-smith);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.disclaimer-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.disclaimer-item h4 {
    color: var(--nevada);
    margin-bottom: 15px;
    font-size: 1.1em;
    font-weight: 600;
}

.disclaimer-item p {
    color: var(--nevada);
    line-height: 1.6;
    margin: 0;
    font-size: 0.95em;
}

.disclaimer-item strong {
    color: var(--granny-smith);
    font-weight: 600;
}

.bmi-info {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e8e8e8;
}

.bmi-category {
    font-weight: 600;
    font-size: 0.9em;
    margin-top: 5px;
}

/* Trust Block Styling */
.trust-block {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 12px;
    border-left: 4px solid var(--granny-smith);
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.trust-icon {
    font-size: 2em;
    flex-shrink: 0;
}

.trust-content h4 {
    margin: 0 0 8px 0;
    font-size: 1.1em;
    color: var(--nevada);
    font-weight: 600;
}

.trust-content p {
    margin: 0;
    font-size: 0.9em;
    color: var(--nevada);
    opacity: 0.8;
    line-height: 1.5;
}

/* Macros Container Styling */
.macros-container {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 15px;
    border-left: 4px solid var(--granny-smith);
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.macros-container h3 {
    margin: 0 0 20px 0;
    font-size: 1.4em;
    color: var(--nevada);
    font-weight: 600;
    text-align: center;
}

.macros-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.macro-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.macro-item:hover {
    transform: translateY(-2px);
}

.macro-icon {
    font-size: 2.5em;
    flex-shrink: 0;
}

.macro-info h4 {
    margin: 0 0 5px 0;
    font-size: 1.1em;
    color: var(--nevada);
    font-weight: 600;
}

.macro-amount {
    margin: 0 0 3px 0;
    font-size: 1.3em;
    font-weight: 700;
    color: var(--granny-smith);
}

.macro-percent {
    margin: 0;
    font-size: 0.85em;
    color: var(--nevada);
    opacity: 0.7;
}

.macro-explanation {
    padding: 20px;
    background: rgba(139, 195, 74, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(139, 195, 74, 0.1);
}

.macro-explanation p {
    margin: 0 0 8px 0;
    font-size: 0.9em;
    color: var(--nevada);
    line-height: 1.5;
}

.macro-explanation p:last-child {
    margin-bottom: 0;
}

.calculator-container {
    max-width: 700px;
    margin: 0 auto;
}

.calculator-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: var(--nevada);
    margin-bottom: 8px;
    font-size: 1em;
}

.form-group input,
.form-group select {
    padding: 15px;
    border: 2px solid var(--loblolly);
    border-radius: 10px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--granny-smith);
    box-shadow: 0 0 0 3px rgba(139, 195, 74, 0.1);
}

.calculate-btn {
    background: linear-gradient(45deg, var(--granny-smith), var(--loblolly));
    color: var(--white);
    border: none;
    padding: 18px 40px;
    border-radius: 50px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
    box-shadow: 0 4px 15px rgba(139, 195, 74, 0.3);
}

.calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 195, 74, 0.4);
}

.result-container {
    margin-top: 40px;
    padding: 30px;
    background: var(--white);
    border-radius: 15px;
    border: 2px solid var(--loblolly);
    text-align: center;
}

.result-header h2 {
    font-size: 2em;
    margin-bottom: 20px;
    font-weight: 700;
    color: var(--nevada);
}

.result-main {
    font-size: 1.4em;
    margin-bottom: 30px;
    line-height: 1.4;
    color: var(--granny-smith);
    font-weight: 600;
}

.result-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 25px;
}

.detail-card {
    padding: 20px;
    background: linear-gradient(135deg, var(--granny-smith), var(--loblolly));
    border-radius: 12px;
    color: var(--white);
    text-align: center;
}

.detail-label {
    font-weight: 700;
    font-size: 1.1em;
    margin-bottom: 5px;
}

.detail-description {
    font-size: 0.9em;
    opacity: 0.9;
    margin-bottom: 10px;
}

.detail-value {
    font-weight: 700;
    font-size: 1.3em;
}

/* FAQ Sidebar Items */
.faq-sidebar .faq-item {
    margin-bottom: 20px;
    padding: 0 0 18px 0;
    border-bottom: 1px solid #e8e8e8;
}

.faq-sidebar .faq-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.faq-sidebar .faq-item h3 {
    font-size: 0.95em;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--nevada);
    line-height: 1.3;
}

.faq-sidebar .faq-item p {
    color: var(--nevada);
    line-height: 1.4;
    margin: 0;
    opacity: 0.8;
    font-size: 0.85em;
}

/* Right Sidebar - DISABLED FOR TESTING */
.right-sidebar {
    /* All styles disabled for testing */
}

/* Sidebar Sections */
.sidebar-section {
    background-color: var(--white);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    padding: 25px;
}

.sidebar-section h2 {
    font-size: 1.3em;
    margin-bottom: 20px;
    font-weight: 600;
    color: var(--nevada);
    text-align: center;
    border-bottom: 2px solid var(--granny-smith);
    padding-bottom: 12px;
}

/* Product Items */
.product-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 10px;
    border-left: 3px solid var(--granny-smith);
    transition: all 0.3s ease;
}

.product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.product-image {
    font-size: 2em;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--granny-smith);
    border-radius: 8px;
    flex-shrink: 0;
}

.product-info h4 {
    font-size: 1em;
    margin: 0 0 5px 0;
    color: var(--nevada);
    font-weight: 600;
}

.product-price {
    font-size: 1.1em;
    font-weight: 700;
    color: var(--granny-smith);
    margin: 0 0 5px 0;
}

.product-description {
    font-size: 0.85em;
    color: var(--nevada);
    opacity: 0.7;
    margin: 0;
    line-height: 1.4;
}

/* Blog Items */
.blog-item {
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 10px;
    border-left: 3px solid var(--granny-smith);
    transition: all 0.3s ease;
}

.blog-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.blog-date {
    font-size: 0.8em;
    color: var(--granny-smith);
    font-weight: 600;
    margin-bottom: 8px;
}

.blog-item h4 {
    font-size: 1em;
    margin: 0 0 8px 0;
    color: var(--nevada);
    font-weight: 600;
    line-height: 1.3;
}

.blog-item p {
    font-size: 0.85em;
    color: var(--nevada);
    opacity: 0.7;
    margin: 0 0 10px 0;
    line-height: 1.4;
}

.blog-link {
    font-size: 0.85em;
    color: var(--granny-smith);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.blog-link:hover {
    color: var(--nevada);
}

/* Mobile responsive for index page */
@media screen and (max-width: 1024px) {
    body:not(.supplementen-page) .categories {
        flex-wrap: wrap;
        justify-content: center;
    }

    body:not(.supplementen-page) .category-box {
        flex: 1 1 45%;
        max-width: 45%;
    }
}

@media screen and (max-width: 768px) {
    body:not(.supplementen-page) .main-content-wrapper {
        padding: 20px;
    }

    body:not(.supplementen-page) .main-content {
        padding: 30px;
    }

    body:not(.supplementen-page) .main-content h1 {
        font-size: 28px;
    }

    body:not(.supplementen-page) .main-content p {
        font-size: 14px;
    }

    body:not(.supplementen-page) .categories {
        flex-direction: column;
        gap: 20px;
    }

    body:not(.supplementen-page) .category-box {
        max-width: 100%;
        height: 250px;
    }

    /* Calculator mobile responsive */
    body.calculator-page .content-container {
        padding: 20px 15px;
    }

    /* Stack layout on mobile - already defined above */

    .faq-sidebar {
        position: static;
        order: 2;
        padding: 25px;
        max-height: none;
        overflow-y: visible;
    }

    .faq-sidebar h2 {
        font-size: 1.3em;
        margin-bottom: 20px;
    }

    .calculator-section {
        padding: 30px 20px;
        border-radius: 10px;
        order: 1;
        position: static;
        max-height: none;
        overflow-y: visible;
    }

    .right-sidebar {
        order: 3;
        margin-top: 20px;
    }

    /* Touch-friendly form elements */
    .form-group input,
    .form-group select {
        min-height: 48px;
        font-size: 16px;
        padding: 12px 15px;
        border-radius: 8px;
        border: 2px solid #e0e0e0;
        transition: all 0.3s ease;
        -webkit-appearance: none;
        appearance: none;
    }

    .form-group input:focus,
    .form-group select:focus {
        border-color: var(--granny-smith);
        box-shadow: 0 0 0 3px rgba(139, 195, 74, 0.1);
        outline: none;
    }

    /* Touch-friendly buttons */
    .calculate-btn {
        min-height: 50px;
        font-size: 16px;
        padding: 15px 30px;
        border-radius: 10px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    /* Mobile macros styling */
    .macros-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .macro-item {
        padding: 15px;
        gap: 12px;
    }

    .macro-icon {
        font-size: 2em;
    }

    .macro-info h4 {
        font-size: 1em;
    }

    .macro-amount {
        font-size: 1.2em;
    }

    .macros-container {
        padding: 20px;
        margin-top: 25px;
    }

    .macros-container h3 {
        font-size: 1.2em;
    }

    .right-sidebar {
        position: static;
        order: 3;
        max-height: none;
        overflow-y: visible;
        gap: 20px;
    }

    .sidebar-section {
        padding: 20px;
    }

    .sidebar-section h2 {
        font-size: 1.2em;
        margin-bottom: 15px;
    }

    .calculator-header h1 {
        font-size: 2em;
    }

    .calculator-header p {
        font-size: 1em;
    }

    .bmi-indicator-container {
        margin: 20px 0;
    }

    .bmi-indicator-display {
        padding: 20px;
        min-height: 140px;
    }

    .bmi-scale {
        max-width: 100%;
    }

    .bmi-scale-bar {
        height: 35px;
    }

    .bmi-labels {
        font-size: 0.7em;
    }

    .bmi-value-display {
        font-size: 0.8em;
        padding: 4px 8px;
    }

    .disclaimer-section {
        padding: 40px 0;
        margin-top: 40px;
    }

    .disclaimer-content h3 {
        font-size: 1.5em;
        margin-bottom: 25px;
    }

    .disclaimer-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .disclaimer-item {
        padding: 20px;
    }

    .disclaimer-item h4 {
        font-size: 1em;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-group input,
    .form-group select {
        padding: 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .calculate-btn {
        padding: 15px 30px;
        font-size: 1em;
    }

    .result-details {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .detail-card {
        padding: 15px;
    }

    .result-main {
        font-size: 1.2em;
    }

    /* FAQ mobile responsive - Clean */
    .faq-section {
        margin-top: 40px;
        padding: 40px 0;
    }

    .faq-section h2 {
        font-size: 1.8em;
        margin-bottom: 40px;
    }

    .faq-item {
        margin-bottom: 25px;
        padding-bottom: 25px;
    }

    .faq-item h3 {
        font-size: 1.2em;
    }

    .product-item,
    .blog-item {
        margin-bottom: 15px;
        padding: 12px;
    }

    .product-item {
        gap: 12px;
    }

    .product-image {
        width: 40px;
        height: 40px;
        font-size: 1.5em;
    }

    .product-info h4,
    .blog-item h4 {
        font-size: 0.95em;
    }
}

@media screen and (max-width: 480px) {
    body.calculator-page .content-container {
        padding: 15px 10px;
    }

    .calculator-section {
        padding: 25px 15px;
    }

    .calculator-header h1 {
        font-size: 1.8em;
    }

    .form-group input,
    .form-group select {
        padding: 10px;
    }

    .calculate-btn {
        padding: 12px 25px;
    }

    .faq-sidebar {
        padding: 20px 15px;
    }

    .faq-sidebar h2 {
        font-size: 1.2em;
    }

    .silhouette-display {
        padding: 15px;
        min-height: 120px;
    }

    .silhouette-svg svg {
        max-height: 100px;
    }

    .sidebar-section h2 {
        font-size: 1.1em;
    }

    .product-item,
    .blog-item {
        padding: 10px;
        margin-bottom: 12px;
    }

    .product-image {
        width: 35px;
        height: 35px;
        font-size: 1.3em;
    }
}

/* Small mobile screens */
@media (max-width: 480px) {
    .header {
        padding: 8px 10px;
    }

    .logo-container img {
        max-width: 40px;
        margin-right: 6px;
    }

    .logo-container h1 {
        font-size: 1.1em;
    }

    .content-container {
        padding: 10px;
    }

    /* Price boxes smaller */
    #min-price-box,
    #max-price-box {
        width: 60px;
        height: 60px;
    }

    .price-box input[type="number"] {
        width: 50px;
    }

    /* Table even more compact */
    .product-table th,
    .product-table td {
        padding: 6px 4px;
        font-size: 12px;
    }

    .product-table img {
        max-width: 70px; /* Much larger for better visibility */
        max-height: 70px;
        border-radius: 4px; /* Rounded corners */
        box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* Subtle shadow */
    }


}