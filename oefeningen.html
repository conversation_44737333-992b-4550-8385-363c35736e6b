<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="SuppRadar Oefeningen - Binnenkort beschikbaar. Ontdek fitness oefeningen en trainingsschema's die perfect aansluiten bij jouw supplementen.">
    <meta name="keywords" content="fitness oefeningen, trainingsschema, workout routines, krachttraining, cardio">
    <meta name="author" content="SuppRadar">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="main.css">
    <title>SuppRadar - Oefeningen (Binnenkort Beschikbaar)</title>
</head>
<body>
    <div class="header">
        <!-- Mobile hamburger menu -->
        <div class="mobile-menu-toggle" id="mobile-menu-toggle">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="logo-container">
            <a href="./index.html" class="logo-link">
                <img src="./Afbeeldingen/Logo.webp" alt="SuppRadar Logo">
                <h1>SuppRadar</h1>
            </a>
        </div>
        <div class="nav-menu" id="nav-menu">
            <a href="./index.html">Home</a>
            <a href="./supplementen.html">Supplementen</a>
            <a href="./calorie-calculator.html">Calorie Calculator</a>
            <div class="dropdown">
                <a href="./shop-per-doel.html">Shop per Doel</a>
                <div class="dropdown-content">
                    <a href="./shop-per-doel.html">Gewichtsverlies</a>
                    <a href="./shop-per-doel.html">Spieropbouw</a>
                    <a href="./shop-per-doel.html">Uithoudingsvermogen</a>
                    <a href="./shop-per-doel.html">Fitness & Onderhoud</a>
                    <a href="./shop-per-doel.html">Gezondheid & Focus</a>
                    <a href="./shop-per-doel.html">Topprestaties</a>
                </div>
            </div>
            <a href="./accessoires.html">Accessoires</a>
            <a href="./oefeningen.html" class="active">Oefeningen</a>
            <a href="./blog.html">Blog</a>
        </div>
    </div>

    <div class="coming-soon-container">
        <div class="coming-soon-content">
            <div class="coming-soon-icon">🏋️</div>
            <h1>Oefeningen</h1>
            <h2>Binnenkort Beschikbaar</h2>
            <p>We ontwikkelen een uitgebreide oefeningendatabase met trainingsschema's die perfect aansluiten bij jouw supplementgebruik. Van krachttraining tot cardio - alles wetenschappelijk onderbouwd en praktisch toepasbaar.</p>
            <div class="coming-soon-features">
                <div class="feature-item">
                    <span class="feature-icon">💪</span>
                    <span>Krachttraining routines</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🏃</span>
                    <span>Cardio & uithoudingstraining</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📋</span>
                    <span>Gepersonaliseerde schema's</span>
                </div>
            </div>
            <a href="./calorie-calculator.html" class="back-button">Bereken Je Caloriebehoefte →</a>
        </div>
    </div>

    <script src="main.js"></script>

    <style>
        .coming-soon-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 80px);
            background: linear-gradient(135deg, var(--loblolly) 0%, var(--granny-smith) 100%);
            padding: 40px 20px;
        }

        .coming-soon-content {
            background: var(--white);
            padding: 60px 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(6, 32, 52, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        .coming-soon-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .coming-soon-content h1 {
            color: var(--black-pearl);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .coming-soon-content h2 {
            color: var(--granny-smith);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 30px;
        }

        .coming-soon-content p {
            color: var(--nevada);
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 40px;
        }

        .coming-soon-features {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            color: var(--river-bed);
            font-weight: 600;
        }

        .feature-icon {
            font-size: 1.2rem;
        }

        .back-button {
            display: inline-block;
            background-color: var(--button-color);
            color: var(--white);
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background-color: var(--hover-button-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(6, 32, 52, 0.2);
        }

        @media (max-width: 768px) {
            .coming-soon-content {
                padding: 40px 20px;
            }

            .coming-soon-content h1 {
                font-size: 2rem;
            }

            .coming-soon-content h2 {
                font-size: 1.3rem;
            }

            .coming-soon-features {
                gap: 12px;
            }
        }
    </style>
</body>
</html>
